<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_saleorder_inherit" inherit_id="sale.report_saleorder_document">
        <!-- Filtrar solo las líneas con order_line_check=True en el cuerpo de la tabla -->
        <xpath expr="//tbody[@class='sale_tbody']" position="replace">
            <tbody class="sale_tbody">
                <!-- Verificar la configuración y mostrar solo líneas con order_line_check=True si está activado -->
                <t t-if="doc.is_show_product_in_sale_report">
                    <t t-set="filtered_lines" t-value="doc.order_line and doc.order_line.filtered(lambda l: l.order_line_check) or []"/>
                    <t t-foreach="filtered_lines" t-as="line">
                        <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                            <t t-if="not line.display_type">
                                <!-- Mostrar el campo 'name' si existe, si no, mostrar 'product_id.display_name' -->
                                <td name="td_name">
                                    <span t-field="line.name" t-if="line.name"/>
                                    <span t-field="line.product_id.display_name" t-if="not line.name"/>
                                </td>
                                <td name="td_quantity" class="text-end">
                                    <span t-field="line.product_uom_qty"/>
                                    <span t-field="line.product_uom"/>
                                    <span t-if="line.product_packaging_id">
                                        (<span t-field="line.product_packaging_qty" t-options='{"widget": "integer"}'/> <span t-field="line.product_packaging_id"/>)
                                    </span>
                                </td>
                                <td name="td_priceunit" class="text-end"><span t-field="line.price_unit"/></td>
                                <td t-if="display_discount" class="text-end"><span t-field="line.discount"/></td>
                                <td name="td_taxes" class="text-end">
                                    <span t-out="', '.join(map(lambda x: (x.description or x.name), line.tax_id))"/>
                                </td>
                                <td t-if="not line.is_downpayment" name="td_subtotal" class="text-end o_price_total">
                                    <span t-field="line.price_subtotal"/>
                                </td>
                            </t>
                            <t t-elif="line.display_type == 'line_section'">
                                <td name="td_section_line" colspan="99"><span t-field="line.name"/></td>
                            </t>
                            <t t-elif="line.display_type == 'line_note'">
                                <td name="td_note_line" colspan="99"><span t-field="line.name"/></td>
                            </t>
                        </tr>
                    </t>
                    <!-- Lógica para mostrar subtotales -->
                    <tr class="is-subtotal text-end">
                        <td name="td_section_subtotal" colspan="99">
                            <strong class="mr16">Subtotal</strong>
                            <!-- Calcular el subtotal dinámico de las líneas filtradas -->
                            <span
                                t-out="sum(line.price_subtotal for line in filtered_lines)"
                                t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                            />
                        </td>
                    </tr>
                </t>
                <!-- Mostrar todas las líneas si la configuración no está activada -->
                <t t-else="">
                    <t t-set="current_subtotal" t-value="0"/>
                    <t t-foreach="doc.order_line" t-as="line">
                        <t t-set="current_subtotal" t-value="current_subtotal + (line.price_subtotal or 0)"/>
                        <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                            <t t-if="not line.display_type">
                                <td name="td_name"><span t-field="line.name"/></td>
                                <td name="td_quantity" class="text-end">
                                    <span t-field="line.product_uom_qty"/>
                                    <span t-field="line.product_uom"/>
                                    <span t-if="line.product_packaging_id">
                                        (<span t-field="line.product_packaging_qty" t-options='{"widget": "integer"}'/> <span t-field="line.product_packaging_id"/>)
                                    </span>
                                </td>
                                <td name="td_priceunit" class="text-end">
                                    <span t-field="line.price_unit"/>
                                </td>
                                <td t-if="display_discount" class="text-end">
                                    <span t-field="line.discount"/>
                                </td>
                                <td name="td_taxes" class="text-end">
                                    <span t-out="', '.join(map(lambda x: (x.description or x.name), line.tax_id))"/>
                                </td>
                                <td t-if="not line.is_downpayment" name="td_subtotal" class="text-end o_price_total">
                                    <span t-field="line.price_subtotal"/>
                                </td>
                            </t>
                            <t t-elif="line.display_type == 'line_section'">
                                <td name="td_section_line" colspan="99">
                                    <span t-field="line.name"/>
                                </td>
                            </t>
                            <t t-elif="line.display_type == 'line_note'">
                                <td name="td_note_line" colspan="99">
                                    <span t-field="line.name"/>
                                </td>
                            </t>
                        </tr>
                    </t>
                    <tr class="is-subtotal text-end">
                        <td name="td_section_subtotal" colspan="99">
                            <strong class="mr16">Subtotal</strong>
                            <span
                                t-out="current_subtotal"
                                t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                            />
                        </td>
                    </tr>
                </t>
            </tbody>
        </xpath>

        <xpath expr="//div[@class='clearfix' and @name='so_total_summary']" position="replace">
            <t t-if="doc.is_show_product_in_sale_report">
                <div id="custom_total" class="row" name="custom_total">
                    <div class="col-6 ms-auto">
                        <!-- Tabla sin borde para evitar líneas adicionales -->
                        <table class="table table-borderless table-sm">
                            <!-- Línea horizontal superior única -->
                            <tr>
                                <td colspan="2" style="border-top: 1px solid #ddd;"></td>
                            </tr>
                            <!-- Importe sin impuestos -->
                            <tr>
                                <td><strong>Importe sin impuestos</strong></td>
                                <td class="text-end">
                                    <span t-field="doc.selected_amount_untaxed" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                </td>
                            </tr>
                            <!-- Detalle de Impuestos (solo mostrar si el monto del impuesto es mayor a 0) -->
                            <t t-if="doc.selected_amount_tax > 0">
                                <tr>
                                    <td>
                                        <!-- Etiqueta del impuesto dinámica con verificación de existencia -->
                                        <t t-if="'groups' in doc.tax_totals">
                                            <span t-foreach="doc.tax_totals['groups']" t-as="tax_group">
                                                <t t-esc="tax_group['name']"/>:
                                            </span>
                                        </t>
                                        <t t-else="">
                                            Impuesto aplicado
                                        </t>
                                    </td>
                                    <td class="text-end">
                                        <span t-field="doc.selected_amount_tax" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                    </td>
                                </tr>
                            </t>
                            <!-- Línea horizontal inferior única -->
                            
                            <!-- Total General -->
                            <tr>
                                <td><strong>Total</strong></td>
                                <td class="text-end">
                                    <span t-field="doc.selected_amount_total" t-options="{'widget': 'monetary', 'display_currency': doc.currency_id}"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </t>
            <!-- Mostrar todas las líneas si la configuración no está activada -->
            <t t-else="">
                <div id="total" class="row" name="total">
                    <div id="total" class="row" name="total">
                        <div t-attf-class="#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'} ms-auto">
                            <table class="table table-sm table-borderless">
                                <!-- Tax totals -->
                                <t t-set="tax_totals" t-value="doc.tax_totals"/>
                                <t t-call="sale.document_tax_totals"/>
                            </table>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>

        <xpath expr="//div[@class='page']/div[@id='informations']" position="replace">
            <div class="row mt-4 mb-2" id="custom_informations">
                <div t-if="doc.client_order_ref" class="col-auto col-3 mw-100 mb-2" name="informations_reference">
                    <strong>Su referencia:</strong><br/>
                    <span class="m-0" t-field="doc.client_order_ref"/>
                </div>
                <div t-if="doc.date_order" class="col-auto col-3 mw-100 mb-2" name="informations_date">

                    <strong t-if="doc.state != 'draft'">Fecha de orden:</strong>
                    <span class="m-0" t-if="doc.state != 'draft'" t-field="doc.date_order" t-options='{"widget": "date"}'/>

                    <strong t-if="doc.state == 'draft'">Fecha de la cotización:</strong><br/>
                    <span class="m-0" t-if="doc.state == 'draft'" t-field="doc.fecha_de_cotizacion" t-options='{"widget": "date"}'/>

                </div>
                <div t-if="doc.validity_date and doc.state in ['draft', 'sent']"
                    class="col-auto col-3 mw-100 mb-2"
                    name="expiration_date">
                    <strong>Vencimiento:</strong><br/>
                    <span class="m-0" t-field="doc.validity_date"/>
                </div>
                <div t-if="doc.inspector.name" class="col-auto col-3 mw-100 mb-2">
                    <strong>Inspector:</strong><br/>
                    <span class="m-0" t-field="doc.inspector"/>
                </div>
            </div>
        </xpath>

        <!-- Insertar el bloque justo después del contenedor de totales -->
        <xpath expr="//div[@class='page']" position="after">
            <t t-if="doc.is_show_product_in_sale_report">
                <div class="mt80 mb40" style="page-break-inside: avoid; clear: both;">
                    <!-- Espacio adicional entre el total y las firmas -->
                    <div class="mt-5"></div>
                    <!-- Firma de recepción, sello y fecha al final del informe -->
                    <div class="row mt-4">
                        <!-- Firma de recepción -->
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Firma de recepción</strong>
                            </p>
                        </div>
                        <!-- Sello -->
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Sello</strong>
                            </p>
                        </div>
                        <!-- Fecha -->
                        <div class="col-3 text-center" style="width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Fecha</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>

    </template>
</odoo>


