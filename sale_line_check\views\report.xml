<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="report_cert_ok_generico_action" model="ir.actions.report">
        <field name="name">Cert OK Generico</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_line_check.report_cert_ok_generico</field>
        <field name="report_file">sale_line_check.report_cert_ok_generico</field>
        <field name="print_report_name">'Certificado - %s' % (object.name)</field>
        <field name="binding_model_id" ref="sale.model_sale_order"/>
        <field name="binding_type">report</field>
    </record>
    <record id="report_pdf_calf_action" model="ir.actions.report">
        <field name="name">Cotización en PDF CALF</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_line_check.report_pdf_calf</field>
        <field name="report_file">sale_line_check.report_pdf_calf</field>
        <field name="print_report_name">'CALF - %s' % (object.name)</field>
        <field name="binding_model_id" ref="sale.model_sale_order"/>
        <field name="binding_type">report</field>
    </record>
    <record id="report_pdf_remito_action" model="ir.actions.report">
        <field name="name">Remito (Completo sin importe)</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_line_check.report_pdf_remito</field>
        <field name="report_file">sale_line_check.report_pdf_remito</field>
        <field name="print_report_name">'Remito - %s' % (object.name)</field>
        <field name="binding_model_id" ref="sale.model_sale_order"/>
        <field name="binding_type">report</field>
    </record>

    <delete id="sale.action_report_pro_forma_invoice" model="ir.actions.report"/>
</odoo>