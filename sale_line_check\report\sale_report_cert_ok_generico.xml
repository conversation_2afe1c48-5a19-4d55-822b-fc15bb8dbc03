<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_cert_ok_generico_template">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.partner_id.lang)" />
            <t t-set="forced_vat" t-value="doc.fiscal_position_id.foreign_vat"/>
            <t t-set="address">
                <div t-field="doc.partner_id"
                    t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
                <p t-if="doc.partner_id.vat">
                    <t t-if="doc.company_id.account_fiscal_country_id.vat_label" t-out="doc.company_id.account_fiscal_country_id.vat_label"/>
                    <t t-else="">ID Tributario</t>: <span t-field="doc.partner_id.vat"/>
                </p>
            </t>

            <div class="page">
                <div class="oe_structure"/>
                <h2 class="mt-4">
                    <span>Cert. de trabajo para facturación # </span>
                    <span t-field="doc.name"/>
                </h2>

                <div class="row mt-4 mb-2" id="informations">
                    <div t-if="doc.client_order_ref" class="col-auto col-3 mw-100 mb-2" name="informations_reference">
                        <strong>Su referencia:</strong><br/>
                        <span class="m-0" t-field="doc.client_order_ref"/>
                    </div>
                    <!-- <div t-if="doc.date_order" class="col-auto col-3 mw-100 mb-2" name="informations_date">
                        <strong t-if="doc.state != 'draft'">Fecha de orden:</strong>
                        <span class="m-0" t-if="doc.state != 'draft'" t-field="doc.date_order" t-options='{"widget": "date"}'/>
                        <strong t-if="doc.state == 'draft'">Fecha de la cotización:</strong><br/>
                        <span class="m-0" t-if="doc.state == 'draft'" t-field="doc.fecha_de_cotizacion" t-options='{"widget": "date"}'/>
                    </div> -->
                    <div t-if="doc.fecha_de_cotizacion and doc.state in ['draft', 'sent', 'sale']"
                        class="col-auto col-3 mw-100 mb-2"
                        name="expiration_date">
                        <strong>Fecha OT:</strong><br/>
                        <span class="m-0" t-field="doc.fecha_de_cotizacion"/>
                    </div>
                    <div t-if="doc.inspector.name" class="col-auto col-3 mw-100 mb-2">
                        <strong>Inspector:</strong><br/>
                        <span class="m-0" t-field="doc.inspector"/>
                    </div>
                </div>

                <t t-set="filtered_lines" t-value="doc.order_line.filtered(lambda l: l.order_line_check)"/>
                <t t-set="display_discount" t-value="any(l.discount for l in filtered_lines)"/>

                <table class="table table-sm o_main_table table-borderless mt-4">
                    <thead>
                        <tr>
                            <th class="text-start">Descripción</th>
                            <th class="text-end">Cantidad</th>
                            <th class="text-end">Precio Unitario</th>
                            <th t-if="display_discount" class="text-end">Desc.%</th>
                            <th class="text-end">Impuestos</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody class="sale_tbody">
                        <t t-if="doc.is_show_product_in_sale_report">
                            <t t-foreach="filtered_lines" t-as="line">
                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td>
                                            <span t-field="line.name" t-if="line.name"/>
                                            <span t-field="line.product_id.display_name" t-if="not line.name"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.product_uom_qty"/>
                                            <span t-field="line.product_uom"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.price_unit"/>
                                        </td>
                                        <td t-if="display_discount" class="text-end">
                                            <span t-field="line.discount"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-out="', '.join(map(lambda x: (x.description or x.name), line.tax_id))"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.price_subtotal"/>
                                        </td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_section'">
                                        <td colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_note'">
                                        <td colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                </tr>
                            </t>
                            <tr class="is-subtotal text-end">
                                <td colspan="99">
                                    <strong>Subtotal</strong>
                                    <span
                                        t-out="sum(line.price_subtotal for line in filtered_lines)"
                                        t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                </td>
                            </tr>
                        </t>

                        <t t-else="">
                            <t t-foreach="doc.order_line" t-as="line">
                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td>
                                            <span t-field="line.name" t-if="line.name"/>
                                            <span t-field="line.product_id.display_name" t-if="not line.name"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.product_uom_qty"/>
                                            <span t-field="line.product_uom"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.price_unit"/>
                                        </td>
                                        <td t-if="display_discount" class="text-end">
                                            <span t-field="line.discount"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-out="', '.join(map(lambda x: (x.description or x.name), line.tax_id))"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="line.price_subtotal"/>
                                        </td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_section'">
                                        <td colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_note'">
                                        <td colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                </tr>
                            </t>
                            <tr class="is-subtotal text-end">
                                <td colspan="99">
                                    <strong>Subtotal</strong>
                                    <span
                                        t-out="sum(line.price_subtotal for line in doc.order_line)"
                                        t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
                <div class="clearfix" name="so_total_summary">
                    <t t-if="doc.is_show_product_in_sale_report">
                        <div class="row">
                            <div class="col-6 ms-auto">
                                <table class="table table-borderless table-sm">
                                    <tr>
                                        <td>Subtotal:</td>
                                        <td class="text-end">
                                            <span t-field="doc.selected_amount_untaxed" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                        </td>
                                    </tr>
                                    <t t-if="doc.selected_amount_tax > 0">
                                        <tr>
                                            <td>Impuestos:</td>
                                            <td class="text-end">
                                                <span t-field="doc.selected_amount_tax" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                            </td>
                                        </tr>
                                    </t>
                                    <tr>
                                        <td><strong>Total</strong></td>
                                        <td class="text-end">
                                            <span t-field="doc.selected_amount_total" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </t>
                    <!-- Mostrar todas las líneas si la configuración no está activada -->
                    <t t-else="">
                        <div id="total" class="row" name="total">
                            <div id="total" class="row" name="total">
                                <div t-attf-class="#{'col-6' if report_type != 'html' else 'col-sm-7 col-md-6'} ms-auto">
                                    <table class="table table-sm table-borderless">
                                        <!-- Tax totals -->
                                        <t t-set="tax_totals" t-value="doc.tax_totals"/>
                                        <t t-call="sale.document_tax_totals"/>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>

                <div t-if="not doc.signature" class="oe_structure"></div>
                <div t-else="" class="mt-4 ml64 mr4" name="signature">
                    <div class="offset-8">
                        <strong>Firma</strong>
                    </div>
                    <div class="offset-8">
                        <img t-att-src="image_data_uri(doc.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                    </div>
                    <div class="offset-8 text-center">
                        <span t-field="doc.signed_by"/>
                    </div>
                </div>
                <div>
                    <span t-field="doc.note" t-attf-style="#{'text-align:justify;text-justify:inter-word;' if doc.company_id.terms_type != 'html' else ''}" name="order_note"/>
                    <p t-if="not is_html_empty(doc.payment_term_id.note)">
                        <span t-field="doc.payment_term_id.note"/>
                    </p>
                    <p t-if="doc.fiscal_position_id and not is_html_empty(doc.fiscal_position_id.sudo().note)"
                        id="fiscal_position_remark">
                        <strong>Nota de Posición Fiscal:</strong>
                        <span t-field="doc.fiscal_position_id.sudo().note"/>
                    </p>
                </div>

                <div class="mt80 mb40" style="page-break-inside: avoid; clear: both;">
                    <div class="row mt-4">
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3"><strong>Entregado por:</strong></p>
                        </div>
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3"><strong>Recibido por:</strong></p>
                        </div>
                        <div class="col-3 text-center" style="width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3"><strong>Fecha:</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="report_cert_ok_generico_raw">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="sale_line_check.report_cert_ok_generico_template" t-lang="doc.partner_id.lang"/>
            </t>
        </t>
    </template>

    <template id="report_cert_ok_generico">
        <t t-call="sale_line_check.report_cert_ok_generico_raw"/>
    </template>
</odoo>
