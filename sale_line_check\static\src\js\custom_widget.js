/** @odoo-module **/

import { registry } from "@web/core/registry";
import { ListRenderer } from "@web/views/list/list_renderer";
import { X2Many<PERSON>ield, x2<PERSON>any<PERSON>ield } from "@web/views/fields/x2many/x2many_field";
import { TextField, ListTextField } from "@web/views/fields/text/text_field";
import { CharField } from "@web/views/fields/char/char_field";
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { Component, useEffect } from "@odoo/owl";

export class CustomSectionAndNoteListRenderer extends ListRenderer {
    /**
     * The purpose of this extension is to allow sections and notes in the one2many list
     * primarily used on Sales Orders and Invoices
     *
     * @override
     */
    setup() {
        super.setup();
        this.titleField = "name";
        useEffect(
            (editedRecord) => this.focusToName(editedRecord),
            () => [this.editedRecord]
        )
    }

    focusToName(editRec) {
        if (editRec && editRec.isNew && this.isSectionOrNote(editRec)) {
            const nameCol = this.state.columns.find((c) => c.name === 'name');
            const checkCol = this.state.columns.find((c) => c.name === 'order_line_check');
            this.focusCell(nameCol || checkCol, null); // Enfoca el primero disponible
        }
    }
    
    isSectionOrNote(record=null) {
        record = record || this.record;
        return ['line_section', 'line_note'].includes(record.data.display_type);
    }

    getRowClass(record) {
        const existingClasses = super.getRowClass(record);
        return `${existingClasses} o_is_${record.data.display_type}`;
    }

    getCellClass(column, record) {
        const classNames = super.getCellClass(column, record);
        if (this.isSectionOrNote(record) && column.widget !== "handle" && !['name', 'order_line_check'].includes(column.name)) {
            return `${classNames} o_hidden`;
        }
        return classNames;
    }

    getColumns(record) {
        const columns = super.getColumns(record);
        if (this.isSectionOrNote(record)) {
            return this.getSectionColumns(columns);
        }
        return columns;
    }

    getSectionColumns(columns) {
        const sectionCols = columns.filter((col) => col.widget === "handle" || ['name', 'order_line_check'].includes(col.name));
        return sectionCols.map((col) => {
            return {
                ...col,
                colspan: (['name', 'order_line_check'].includes(col.name)) ? columns.length - sectionCols.length + 1 : 1
            };
        });
    }
}
CustomSectionAndNoteListRenderer.template = "account.sectionAndNoteListRenderer";

export class SectionAndNoteFieldOne2Many extends X2ManyField {}
SectionAndNoteFieldOne2Many.components = {
    ...X2ManyField.components,
    ListRenderer: CustomSectionAndNoteListRenderer,
};

export const sectionAndNoteFieldOne2Many = {
    ...x2ManyField,
    component: SectionAndNoteFieldOne2Many,
    additionalClasses: [...x2ManyField.additionalClasses || [], "o_field_one2many"],
};

registry.category("fields").add("custom_section_and_note_one2many", sectionAndNoteFieldOne2Many);
