<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.order_line_check</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='sale_order_template_id']" position="after">
                <field name="is_show_product_in_sale_report" />
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="inspector"/>
            </xpath>

            <xpath expr="//form[1]/sheet[1]/group[@name='sale_header']/group[@name='order_details']/div[not(@name)][3]" position="after">
                <field name="fecha_de_cotizacion"/>
            </xpath>
            <!-- Agrega el botón "Seleccionar Todos" en la vista de formulario de sale.order -->
            <xpath expr="//div[@name='so_button_below_order_lines']" position="inside">
                <button name="action_select_all_lines" string="Selec/Desel" type="object" class="btn btn-secondary" invisible="is_show_product_in_sale_report == False or state in ['draft', 'cancel']"/>
            </xpath>

            <!-- Añadir el campo booleano en cada línea del pedido, invisible si el estado es draft o cancel -->
            <xpath expr="//field[@name='order_line']/tree" position="inside">
                <field name="state" column_invisible="True"/>
                <field name="is_show_product_in_sale_report" column_invisible="True"/>
                <field name="order_line_check" widget="boolean_toggle" invisible="is_show_product_in_sale_report == False or state in ['draft', 'cancel']" optional="hide"/>
            </xpath>

            <!-- Modificar el widget del campo order_line -->
            <xpath expr="//field[@name='order_line']" position="attributes">
                <attribute name="widget">custom_section_and_note_one2many</attribute>
            </xpath>
        </field>
    </record>

    <record id="sale_order_tree_inherit" model="ir.ui.view">
        <field name="name">sale.order.tree.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_to_invoice']" position="after">
                <field name="amount_invoiced" string="Facturado" optional="show"/>
                <field name="selected_amount_to_invoice" string="Falta facturar" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="view_quotation_tree_revert" model="ir.ui.view">
        <field name="name">sale.order.tree.revert</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_quotation_tree"/> <!-- Referenciar a la vista que deseas revertir -->
        <field name="priority">5</field>
        <field name="arch" type="xml">
            
            <!-- Restaurar el atributo string al valor original -->
            <xpath expr="//field[@name='amount_to_invoice']" position="after">
                <field name="selected_amount_to_invoice" string="Falta facturar" optional="show" sum="Total a facturar"/>
            </xpath>
            <!-- Restaurar el atributo string al valor original -->
            <xpath expr="//field[@name='create_date']" position="after">
                <field name="fecha_de_cotizacion" string="Fecha OT" optional="show"/>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="inspector" string="inspector" optional="show"/>
            </xpath>
        </field>
    </record>


</odoo>




