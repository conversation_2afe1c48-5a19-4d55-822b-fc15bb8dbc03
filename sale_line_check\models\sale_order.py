from odoo import api, models, fields

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    inspector = fields.Many2one(
        'res.partner', 
        string='Inspector'
    )
    fecha_de_cotizacion = fields.Date(string="Fecha OT")

    # Campo en sale.order que depende del valor de configuración
    is_show_product_in_sale_report = fields.Boolean(
        string="Seleccionar líneas del pedido a imprimir",
        help="Permite seleccionar las líneas del pedido que se mostrarán en el informe."
    )
    
    selected_amount_to_invoice = fields.Monetary(
        string='Falta facturar',
        store=True,
        currency_field='currency_id'
    )
    
    
    # Campos personalizados para almacenar los totales de líneas seleccionadas
    selected_amount_untaxed = fields.Monetary(string="Untaxed Amount (Selected)", compute='_compute_custom_amounts', store=True)
    selected_amount_tax = fields.Monetary(string="Tax Amount (Selected)", compute='_compute_custom_amounts', store=True)
    selected_amount_total = fields.Monetary(string="Total Amount (Selected)", compute='_compute_custom_amounts', store=True)
    
            
    @api.depends('invoice_ids.state', 'currency_id', 'amount_total')
    def _compute_amount_to_invoice(self):
        for order in self:
            # Si el pedido ya está totalmente facturado, se fija el monto a 0.
            if order.invoice_status == 'invoiced':
                order.amount_to_invoice = 0.0
                order.selected_amount_to_invoice = 0.0
                continue  # Pasamos al siguiente pedido

            # Se parte del total del pedido
            order.amount_to_invoice = order.amount_total

            # Se filtran las facturas en estado 'posted'
            posted_invoices = order.invoice_ids.filtered(lambda inv: inv.state == 'posted')
            if posted_invoices:
                # Si existen facturas asociadas, se restan los importes facturados
                for invoice in posted_invoices:
                    # Se suman los totales de las líneas de la factura vinculadas al pedido
                    prices = sum(
                        invoice.line_ids.filtered(lambda line: order in line.sale_line_ids.order_id).mapped('price_total')
                    )
                    # Se convierte el importe de la factura a la moneda del pedido
                    invoice_amount_currency = invoice.currency_id._convert(
                        prices * -invoice.direction_sign,
                        order.currency_id,
                        invoice.company_id,
                        invoice.date,
                    )
                    order.amount_to_invoice -= invoice_amount_currency
                # Se asigna el monto pendiente calculado a selected_amount_to_invoice
                order.selected_amount_to_invoice = order.amount_to_invoice
            else:
                # Si no hay facturas asociadas en estado 'posted', se asigna el total del pedido
                order.selected_amount_to_invoice = order.amount_total

    
            

    @api.depends('order_line.price_subtotal', 'order_line.price_tax', 'order_line.price_total', 'order_line.order_line_check')
    def _compute_custom_amounts(self):
        """Calcula los totales de las líneas seleccionadas y los asigna a campos personalizados."""
        for order in self:
            # Filtra las líneas seleccionadas para el cálculo del total
            selected_lines = order.order_line.filtered(lambda line: not line.display_type and line.order_line_check)

            # Inicializa los montos en cero
            amount_untaxed = 0.0
            amount_tax = 0.0

            # Calcula el total según el método de redondeo de impuestos de la compañía
            if order.company_id.tax_calculation_rounding_method == 'round_globally':
                tax_results = self.env['account.tax']._compute_taxes([
                    line._convert_to_tax_base_line_dict() for line in selected_lines
                ])
                totals = tax_results['totals']
                amount_untaxed = totals.get(order.currency_id, {}).get('amount_untaxed', 0.0)
                amount_tax = totals.get(order.currency_id, {}).get('amount_tax', 0.0)
            else:
                # Si no es redondeo global, suma directamente los valores de cada línea seleccionada
                amount_untaxed = sum(selected_lines.mapped('price_subtotal'))
                amount_tax = sum(selected_lines.mapped('price_tax'))

            # Calcula el total y asigna los valores a los campos personalizados
            order.selected_amount_untaxed = amount_untaxed
            order.selected_amount_tax = amount_tax
            order.selected_amount_total = amount_untaxed + amount_tax





    def action_select_all_lines(self):
        """Selecciona todas las líneas o las deselecciona en función de su estado actual."""
        for order in self:
            # Determina si la mayoría de las líneas están seleccionadas
            all_selected = all(line.order_line_check for line in order.order_line)
            
            # Si todas las líneas están seleccionadas, las deselecciona; si no, las selecciona
            new_state = not all_selected
            
            # Aplica el nuevo estado a todas las líneas
            for line in order.order_line:
                line.order_line_check = new_state
                
    
    def action_print_cert_ok_generico(self):
        return self.env.ref('sale_line_check.report_cert_ok_generico_action').report_action(self)


    def action_print_cotizacion_pdf_calf(self):
        return self.env.ref('sale_line_check.report_cotizacion_pdf_calf').report_action(self)

    def action_print_cotizacion_orden(self):
        return self.env.ref('sale_line_check.report_cotizacion_orden').report_action(self)

    def action_print_factura_proforma(self):
        return self.env.ref('sale_line_check.report_factura_proforma').report_action(self)

    def action_print_remito_completo(self):
        return self.env.ref('sale_line_check.report_remito_completo').report_action(self)

