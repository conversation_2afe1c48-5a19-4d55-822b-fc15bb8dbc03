<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_pdf_remito_template">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.partner_id.lang)" />
            <t t-set="forced_vat" t-value="doc.fiscal_position_id.foreign_vat"/> <!-- Para que aparezca en el pie del informe en lugar del CUIT de la empresa si está configurado -->
            <t t-set="address">
                <div t-field="doc.partner_id"
                    t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
                <p t-if="doc.partner_id.vat">
                    <t t-if="doc.company_id.account_fiscal_country_id.vat_label" t-out="doc.company_id.account_fiscal_country_id.vat_label"/>
                    <t t-else="">ID Tributario</t>: <span t-field="doc.partner_id.vat"/>
                </p>
            </t>
            <t t-if="doc.partner_shipping_id == doc.partner_invoice_id
                                    and doc.partner_invoice_id != doc.partner_id
                                    or doc.partner_shipping_id != doc.partner_invoice_id">
                <t t-set="information_block">
                    <strong>
                        <t t-if="doc.partner_shipping_id == doc.partner_invoice_id">
                            Dirección de Facturación y Envío:
                        </t>
                        <t t-else="">
                            Dirección de Facturación:
                        </t>
                    </strong>
                    <div t-field="doc.partner_invoice_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                    <t t-if="doc.partner_shipping_id != doc.partner_invoice_id">
                        <strong>Dirección de Envío:</strong>
                        <div t-field="doc.partner_shipping_id"
                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                    </t>
                </t>
            </t>
            <div class="page">
                <div class="oe_structure"/>
                <h2 class="mt-4">
                    <span>Remito Nro: </span>
                    <span t-field="doc.name"/>
                </h2>

                <div class="row mt-4 mb-2" id="informations">
                    <div t-if="doc.client_order_ref" class="col-auto col-3 mw-100 mb-2" name="informations_reference">
                        <strong>Su referencia:</strong><br/>
                        <span class="m-0" t-field="doc.client_order_ref"/>
                    </div>
                    <!-- <div t-if="doc.date_order" class="col-auto col-3 mw-100 mb-2" name="informations_date">
                        <strong t-if="doc.state != 'draft'">Fecha de solicitud:</strong>
                        <span class="m-0" t-if="doc.state != 'draft'" t-field="doc.date_order" t-options='{"widget": "date"}'/>
                        <strong t-if="doc.state == 'draft'">Fecha de la cotización:</strong><br/>
                        <span class="m-0" t-if="doc.state == 'draft'" t-field="doc.fecha_de_cotizacion" t-options='{"widget": "date"}'/>
                    </div> -->
                    <div t-if="doc.fecha_de_cotizacion and doc.state in ['draft', 'sent','sale']"
                        class="col-auto col-3 mw-100 mb-2"
                        name="fecha_de_cotizacion">
                        <strong>Fecha OT:</strong><br/>
                        <span class="m-0" t-field="doc.fecha_de_cotizacion"/>
                    </div>
                    <div t-if="doc.inspector.name" class="col-auto col-3 mw-100 mb-2">
                        <strong>Inspector:</strong><br/>
                        <span class="m-0" t-field="doc.inspector"/>
                    </div>
                </div>
                

                <t t-set="lines_to_report" t-value="doc._get_order_lines_to_report()"/>
                <t t-set="display_discount" t-value="any(l.discount for l in lines_to_report)"/>

                <div class="oe_structure"></div>
                <table class="table table-sm o_main_table table-borderless mt-4">
                    <thead style="display: table-row-group">
                        <tr>
                            <th name="th_description" class="text-start">Descripción</th>
                            <th name="th_quantity" class="text-end">Cantidad</th>
                        </tr>
                    </thead>
                    <tbody class="sale_tbody">
                        <!-- Condición para aplicar la lógica personalizada -->
                        <t t-if="doc.is_show_product_in_sale_report">
                            <t t-set="current_subtotal" t-value="0"/>
                            <t t-foreach="doc.order_line.filtered(lambda l: l.order_line_check)" t-as="line">
                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td name="td_name">
                                            <span t-field="line.name" t-if="line.name"/>
                                            <span t-field="line.product_id.display_name" t-if="not line.name"/>
                                        </td>
                                        <td name="td_quantity" class="text-end">
                                            <span t-field="line.product_uom_qty"/>
                                            <span t-field="line.product_uom"/>
                                            <span t-if="line.product_packaging_id">
                                                (<span t-field="line.product_packaging_qty" t-options='{"widget": "integer"}'/> 
                                                <span t-field="line.product_packaging_id"/>)
                                            </span>
                                        </td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_section'">
                                        <td name="td_section_line" colspan="99"><span t-field="line.name"/></td>
                                        <t t-set="current_section" t-value="line"/>
                                        <t t-set="current_subtotal" t-value="0"/>
                                    </t>
                                    <t t-elif="line.display_type == 'line_note'">
                                        <td name="td_note_line" colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                </tr>
                            </t>
                        </t>
                        <!-- Lógica original para manejar líneas si no aplica 'is_show_product_in_sale_report' -->
                        <t t-else="">
                            <t t-set="current_subtotal" t-value="0"/>
                            <t t-foreach="lines_to_report" t-as="line">
                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>
                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td name="td_name">
                                            <span t-field="line.name" t-if="line.name"/>
                                            <span t-field="line.product_id.display_name" t-if="not line.name"/>
                                        </td>
                                        <td name="td_quantity" class="text-end">
                                            <span t-field="line.product_uom_qty"/>
                                            <span t-field="line.product_uom"/>
                                        </td>
                                    </t>
                                    <t t-elif="line.display_type == 'line_section'">
                                        <td name="td_section_line" colspan="99"><span t-field="line.name"/></td>
                                        <t t-set="current_section" t-value="line"/>
                                        <t t-set="current_subtotal" t-value="0"/>
                                    </t>
                                    <t t-elif="line.display_type == 'line_note'">
                                        <td name="td_note_line" colspan="99"><span t-field="line.name"/></td>
                                    </t>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                </table>

                <div t-if="not doc.signature" class="oe_structure"></div>
                <div t-else="" class="mt-4 ml64 mr4" name="signature">
                    <div class="offset-8">
                        <strong>Firma</strong>
                    </div>
                    <div class="offset-8">
                        <img t-att-src="image_data_uri(doc.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                    </div>
                    <div class="offset-8 text-center">
                        <span t-field="doc.signed_by"/>
                    </div>
                </div>
                
                <div class="mt80 mb40" style="page-break-inside: avoid; clear: both;">
                    
                    <div class="row mt-4">
                        <!-- Firma de recepción -->
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Entrgado por:</strong>
                            </p>
                        </div>
                        <!-- Sello -->
                        <div class="col-3 text-center" style="margin-right: 5%; width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Recibido por:</strong>
                            </p>
                        </div>
                        <!-- Fecha -->
                        <div class="col-3 text-center" style="width: 28%;">
                            <div style="border-bottom: 1px solid #000; width: 100%; height: 50px;"></div>
                            <p class="mt-3">
                                <strong>Fecha</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>


    <!-- Nueva plantilla completa -->
    <template id="report_pdf_remito_raw">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="sale_line_check.report_pdf_remito_template" t-lang="doc.partner_id.lang"/>
            </t>
        </t>
    </template>


    <template id="report_pdf_remito">
        <t t-call="sale_line_check.report_pdf_remito_raw"/>
    </template>
</odoo>
